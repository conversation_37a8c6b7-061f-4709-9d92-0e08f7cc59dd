services:
  app1:
    build: .
    env_file: .env
    deploy:
      resources:
        limits:
          cpus: "2"
          memory: 3g
    ports:
      - "3000:3000"
    restart: always
    healthcheck:
      test: ["CMD", "wget", "--no-verbose", "--tries=1", "--spider", "http://localhost:3000/healthz"]
      interval: 20s
      timeout: 10s
      retries: 3
      start_period: 30s
    # Add extra_hosts to allow container to access host services
    extra_hosts:
      - "host.docker.internal:host-gateway"
    environment:
      - NODE_ENV=production
      - PORT=3000
      - INSTANCE_ID=1
      # Connect to PostgreSQL on VPS host (from Docker container)
      - PGHOST=**********
      - PGPORT=5432
      - PGDATABASE=postgres
      - PGUSER=postgres
      - PGPASSWORD=f9m6Vxgj784xqkLA45w2A
      - PGSSLMODE=${PGSSLMODE:-disable}
      - DATABASE_URL=${DATABASE_URL}
      - PG_MAX_CONNECTIONS=${PG_MAX_CONNECTIONS:-20}
      - PG_MIN_CONNECTIONS=${PG_MIN_CONNECTIONS:-5}
      - PG_IDLE_TIMEOUT=${PG_IDLE_TIMEOUT:-20000}
      # Redis configuration (local Docker service)
      - REDIS_ENABLED=${REDIS_ENABLED:-true}
      - REDIS_HOST=redis
      - REDIS_PORT=6379
      - REDIS_PASSWORD=${REDIS_PASSWORD}
      - REDIS_DEFAULT_TTL=${REDIS_DEFAULT_TTL:-7200}
      # Application settings
      - CONNECTION_WARMUP_INTERVAL=${CONNECTION_WARMUP_INTERVAL:-45000}
      - MEMORY_CACHE_SIZE=${MEMORY_CACHE_SIZE:-200}
      - MAX_MEMORY_CACHE_ITEMS=${MAX_MEMORY_CACHE_ITEMS:-1000}
      - API_URL=${API_URL}
      - INTERNAL_API_KEY=${INTERNAL_API_KEY}
      - ENCRYPTION_KEY=${ENCRYPTION_KEY}
      - RAPIDAPI_PROXY_SECRET=${RAPIDAPI_PROXY_SECRET}
      # Additional environment variables from .env
      - ADMIN_API_KEY=${ADMIN_API_KEY}
      - DB_AUTO_MIGRATE=${DB_AUTO_MIGRATE:-true}
      - DB_INIT_ON_START=${DB_INIT_ON_START:-true}
      - ENABLE_ATTACHMENTS=${ENABLE_ATTACHMENTS:-true}
      - ENABLE_DOMAIN_PRELOADING=${ENABLE_DOMAIN_PRELOADING:-true}
      - ENABLE_PERFORMANCE_MONITORING=${ENABLE_PERFORMANCE_MONITORING:-false}
      - ENABLE_REQUEST_LOGGING=${ENABLE_REQUEST_LOGGING:-true}
      - FORCE_CACHE=${FORCE_CACHE:-true}
      - FREE_USER_INBOX_LIMIT=${FREE_USER_INBOX_LIMIT:-10}
      - LOG_LEVEL=${LOG_LEVEL:-info}
      - MAX_LOG_FILES=${MAX_LOG_FILES:-10}
      - MAX_LOG_SIZE=${MAX_LOG_SIZE:-5}
      - RATE_LIMIT_MAX_REQUESTS=${RATE_LIMIT_MAX_REQUESTS:-600}
      - RATE_LIMIT_WINDOW_MS=${RATE_LIMIT_WINDOW_MS:-60000}
    volumes:
      - type: bind
        source: ./logs
        target: /app/logs
    depends_on:
      redis:
        condition: service_healthy
    networks:
      - tempmail-network

  redis:
    image: redis:7-alpine
    deploy:
      resources:
        limits:
          cpus: "1"
          memory: 1g
    volumes:
      - redis_data:/data
      - ./redis.conf:/usr/local/etc/redis/redis.conf
    command: ["redis-server", "/usr/local/etc/redis/redis.conf", "--requirepass", "${REDIS_PASSWORD}"]
    restart: always
    healthcheck:
      test: ["CMD", "redis-cli", "--raw", "ping"]
      interval: 30s
      timeout: 5s
      retries: 3
      start_period: 10s
    networks:
      - tempmail-network

volumes:
  redis_data:

networks:
  tempmail-network:
    driver: bridge